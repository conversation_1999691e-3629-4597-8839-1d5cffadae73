#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os
from pathlib import Path
import logging
import re

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleCloner:
    def __init__(self, excel_file="import1.xlsx", template_file="Nhan-hop_3.html", output_folder="output_html"):
        """
        Khởi tạo SimpleCloner - chỉ thay thế text, không động QR code
        """
        self.excel_file = excel_file
        self.template_file = template_file
        self.output_folder = output_folder
        
        # Tạo thư mục output nếu chưa tồn tại
        Path(self.output_folder).mkdir(exist_ok=True)
        
        # Đọc dữ liệu từ Excel
        self.data = self.load_excel_data()
        
        # Đọc HTML template gốc
        self.html_template = self.load_html_template()
        
    def load_excel_data(self):
        """Đ<PERSON><PERSON> dữ liệu từ file Excel"""
        try:
            df = pd.read_excel(self.excel_file)
            
            # Đ<PERSON>i tên cột
            column_mapping = {
                'Vị trí': 'vi_tri',
                'Tên chứng từ': 'ten_chung_tu', 
                'Năm': 'nam',
                'Mã hồ sơ': 'ma_ho_so',
                'Thời gian lưu': 'thoi_gian_luu'
            }
            df = df.rename(columns=column_mapping)
            
            # Loại bỏ các dòng trống
            df = df.dropna(subset=['vi_tri', 'ma_ho_so'])
            
            logger.info(f"Đã đọc {len(df)} dòng dữ liệu từ {self.excel_file}")
            return df
        except Exception as e:
            logger.error(f"Lỗi khi đọc file Excel: {e}")
            raise
    
    def load_html_template(self):
        """Đọc file HTML template gốc"""
        try:
            with open(self.template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            logger.info(f"Đã đọc HTML template từ {self.template_file}")
            return content
        except Exception as e:
            logger.error(f"Lỗi khi đọc HTML template: {e}")
            raise
    
    def replace_text_only(self, html_content, row_data):
        """
        Chỉ thay thế text, không động QR code
        """
        # In ra dữ liệu để debug
        print(f"Thay thế dữ liệu cho: {row_data.get('ma_ho_so', '')}")
        print(f"  Năm: {row_data.get('nam', '')}")
        print(f"  Vị trí: {row_data.get('vi_tri', '')}")
        print(f"  Mã hồ sơ: {row_data.get('ma_ho_so', '')}")
        print(f"  Thời gian lưu: {row_data.get('thoi_gian_luu', '')}")
        
        # Thay thế năm - tìm pattern "2007" hoặc "NAM 2007"
        nam_moi = str(row_data.get('nam', ''))
        html_content = html_content.replace('2007', nam_moi)
        
        # Thay thế mã hồ sơ - tìm pattern "KTO2007-1"
        ma_ho_so_moi = str(row_data.get('ma_ho_so', ''))
        html_content = html_content.replace('KTO2007-1', ma_ho_so_moi)
        
        # Thay thế vị trí - tìm pattern "E324-A-1-1-1" hoặc tương tự
        vi_tri_moi = str(row_data.get('vi_tri', ''))
        # Tìm và thay thế các pattern vị trí có thể có
        html_content = re.sub(r'E324[^<\s]*', vi_tri_moi, html_content)
        
        # Thay thế "không giới hạn"
        thoi_gian_luu_moi = str(row_data.get('thoi_gian_luu', ''))
        html_content = html_content.replace('không giới hạn', thoi_gian_luu_moi)
        
        # Thay thế tên chứng từ nếu có
        ten_chung_tu_moi = str(row_data.get('ten_chung_tu', ''))
        if 'Chứng từ thu chi' in html_content:
            html_content = html_content.replace('Chứng từ thu chi', ten_chung_tu_moi)
        
        return html_content
    
    def create_single_html(self, row_data, row_index):
        """Tạo một file HTML cho một dòng dữ liệu"""
        try:
            # Tạo tên file output
            ma_ho_so = row_data.get('ma_ho_so', f'Row_{row_index}')
            output_filename = f"nhan_hop_{row_index + 1}_{ma_ho_so}.html"
            output_path = os.path.join(self.output_folder, output_filename)
            
            # Clone và thay thế dữ liệu
            html_content = self.replace_text_only(self.html_template, row_data)
            
            # Lưu file HTML
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"Đã tạo thành công file: {output_filename}")
            
        except Exception as e:
            logger.error(f"Lỗi khi tạo HTML cho dòng {row_index + 1}: {e}")

def main():
    """Test với 3 dòng đầu tiên"""
    try:
        print("Bắt đầu clone HTML đơn giản (chỉ thay text)...")
        
        # Khởi tạo cloner
        cloner = SimpleCloner("import1.xlsx", "Nhan-hop_3.html", "output_html")
        
        # Test với 3 dòng đầu tiên
        print("\nTạo HTML cho 3 dòng đầu tiên:")
        for index, row in cloner.data.head(3).iterrows():
            print(f"\n--- Dòng {index + 1} ---")
            cloner.create_single_html(row, index)
        
        print(f"\nHoàn thành! Kiểm tra thư mục output_html để xem kết quả.")
        
    except Exception as e:
        print(f"Lỗi: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

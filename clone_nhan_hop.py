#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import qrcode
import base64
import io
import os
from pathlib import Path
import logging
import re

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NhanHopCloner:
    def __init__(self, excel_file="import1.xlsx", template_file="Nhan-hop_3.html", output_folder="output_html"):
        """
        Khởi tạo NhanHopCloner
        
        Args:
            excel_file (str): Đường dẫn đến file Excel chứa dữ liệu
            template_file (str): File HTML gốc để clone
            output_folder (str): Thư mục lưu file HTML đã tạo
        """
        self.excel_file = excel_file
        self.template_file = template_file
        self.output_folder = output_folder
        
        # T<PERSON><PERSON> thư mục output nếu chưa tồn tại
        Path(self.output_folder).mkdir(exist_ok=True)
        
        # Đọc dữ liệu từ Excel
        self.data = self.load_excel_data()
        
        # Đọc HTML template gốc
        self.html_template = self.load_html_template()
        
    def load_excel_data(self):
        """Đọc dữ liệu từ file Excel"""
        try:
            # Đọc file Excel
            df = pd.read_excel(self.excel_file)
            
            # Đổi tên cột để dễ sử dụng
            column_mapping = {
                'Vị trí': 'vi_tri',
                'Tên chứng từ': 'ten_chung_tu', 
                'Năm': 'nam',
                'Mã hồ sơ': 'ma_ho_so',
                'Thời gian lưu': 'thoi_gian_luu'
            }
            df = df.rename(columns=column_mapping)
            
            # Loại bỏ các dòng trống
            df = df.dropna(subset=['vi_tri', 'ma_ho_so'])
            
            logger.info(f"Đã đọc {len(df)} dòng dữ liệu từ {self.excel_file}")
            return df
        except Exception as e:
            logger.error(f"Lỗi khi đọc file Excel: {e}")
            raise
    
    def load_html_template(self):
        """Đọc file HTML template gốc"""
        try:
            with open(self.template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            logger.info(f"Đã đọc HTML template từ {self.template_file}")
            return content
        except Exception as e:
            logger.error(f"Lỗi khi đọc HTML template: {e}")
            raise
    
    def generate_qr_code_base64(self, data):
        """Tạo mã QR và trả về dưới dạng base64"""
        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(data)
            qr.make(fit=True)
            
            qr_img = qr.make_image(fill_color="black", back_color="white")
            
            # Convert to base64
            buffer = io.BytesIO()
            qr_img.save(buffer, format='PNG')
            qr_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            return f"data:image/png;base64,{qr_base64}"
        except Exception as e:
            logger.warning(f"Không thể tạo QR code: {e}")
            return ""
    
    def replace_data_in_html(self, html_content, row_data):
        """
        Thay thế dữ liệu trong HTML content
        
        Args:
            html_content (str): Nội dung HTML gốc
            row_data: Dữ liệu của một dòng từ Excel
            
        Returns:
            str: HTML content đã được thay thế dữ liệu
        """
        # Tạo QR code mới
        qr_data = row_data.get('ma_ho_so', '')
        new_qr_base64 = self.generate_qr_code_base64(qr_data) if qr_data else ""
        
        # Thay thế QR code (tìm và thay thế base64 image cũ)
        if new_qr_base64:
            # Tìm pattern của base64 image trong HTML
            qr_pattern = r'src="data:image/png;base64,[^"]*"'
            html_content = re.sub(qr_pattern, f'src="{new_qr_base64}"', html_content)
        
        # Thay thế các text trong HTML theo yêu cầu cụ thể

        # 1. Thay thế "HỒ SƠ KẾ TOÁN NĂM 2022" thành tên chứng từ mới
        ten_chung_tu_moi = str(row_data.get('ten_chung_tu', ''))
        if ten_chung_tu_moi:
            html_content = re.sub(r'HỒ SƠ KẾ TOÁN NĂM \d{4}', ten_chung_tu_moi, html_content, flags=re.IGNORECASE)

        # 2. Thay thế "31/12/2032" thành thời hạn lưu mới
        thoi_gian_luu_moi = str(row_data.get('thoi_gian_luu', ''))
        if thoi_gian_luu_moi:
            html_content = re.sub(r'31/12/2032', thoi_gian_luu_moi, html_content)

        # 3. Thay thế "KTO-2022-01" thành mã hồ sơ mới
        ma_ho_so_moi = str(row_data.get('ma_ho_so', ''))
        if ma_ho_so_moi:
            html_content = re.sub(r'KTO-\d{4}-\d+', ma_ho_so_moi, html_content)

        # 4. Thay thế vị trí (giữ nguyên như cũ)
        vi_tri_moi = str(row_data.get('vi_tri', ''))
        if vi_tri_moi:
            html_content = re.sub(r'E324[^<]*', vi_tri_moi, html_content)

        # 5. Thay thế năm trong các pattern khác nếu cần
        nam_moi = str(row_data.get('nam', ''))
        if nam_moi:
            html_content = re.sub(r'NAM \d{4}', f'NAM {nam_moi}', html_content, flags=re.IGNORECASE)
        
        return html_content
    
    def create_single_html(self, row_data, row_index):
        """
        Tạo một file HTML cho một dòng dữ liệu
        
        Args:
            row_data: Dữ liệu của một dòng từ Excel
            row_index (int): Chỉ số dòng
        """
        try:
            # Tạo tên file output
            ma_ho_so = row_data.get('ma_ho_so', f'Row_{row_index}')
            output_filename = f"nhan_hop_{row_index + 1}_{ma_ho_so}.html"
            output_path = os.path.join(self.output_folder, output_filename)
            
            # Clone và thay thế dữ liệu
            html_content = self.replace_data_in_html(self.html_template, row_data)
            
            # Lưu file HTML
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"Đã tạo thành công file: {output_filename}")
            
        except Exception as e:
            logger.error(f"Lỗi khi tạo HTML cho dòng {row_index + 1}: {e}")
    
    def create_all_html(self):
        """Tạo tất cả các file HTML"""
        logger.info("Bắt đầu clone và tạo tất cả các file HTML...")
        
        for index, row in self.data.iterrows():
            self.create_single_html(row, index)
        
        logger.info(f"Hoàn thành tạo {len(self.data)} file HTML")

def main():
    """Hàm main để chạy chương trình"""
    try:
        # Khởi tạo cloner
        cloner = NhanHopCloner("import1.xlsx", "Nhan-hop_3.html", "output_html")
        
        # Test với 3 dòng đầu tiên
        print("Tạo HTML cho 3 dòng đầu tiên để test...")
        for index, row in cloner.data.head(3).iterrows():
            cloner.create_single_html(row, index)
        
        print(f"Clone HTML hoàn tất! Kiểm tra thư mục output_html để xem kết quả.")
        
    except Exception as e:
        logger.error(f"Lỗi trong quá trình clone HTML: {e}")
        print(f"Có lỗi xảy ra: {e}")

if __name__ == "__main__":
    main()

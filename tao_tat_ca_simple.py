#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script để tạo tất cả 522 file HTML từ Nhan-hop_3.html
"""

try:
    from clone_simple import SimpleCloner
    
    print("Bắt đầu tạo TẤT CẢ HTML từ Nhan-hop_3.html...")
    print(f"File Excel: import1.xlsx (522 dòng)")
    print(f"HTML Template: Nhan-hop_3.html")
    print(f"Thư mục output: output_html")
    
    # Khởi tạo cloner
    cloner = SimpleCloner("import1.xlsx", "Nhan-hop_3.html", "output_html")
    
    # Tạo tất cả file HTML
    print(f"\nBắt đầu tạo {len(cloner.data)} file HTML...")
    for index, row in cloner.data.iterrows():
        cloner.create_single_html(row, index)
        
        # Hiển thị tiến độ mỗi 50 file
        if (index + 1) % 50 == 0:
            print(f"Đã tạo {index + 1}/{len(cloner.data)} file...")
    
    print(f"\n🎉 HOÀN THÀNH! Đã tạo {len(cloner.data)} file HTML trong thư mục output_html.")
    
except Exception as e:
    print(f"Lỗi: {e}")
    import traceback
    traceback.print_exc()
